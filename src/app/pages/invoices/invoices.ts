import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any = {};

    constructor(
        private data: DataService,
        private commonService: CommonService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        console.log('InvoicesComponent initialized');
        this.loadOrders();
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        // Set auth token for API call (same as in checkout)
        localStorage.setItem('auth_token', 'test-token');

        this.commonService.get('get_all_orders').subscribe({
            next: (response) => {
                console.log('Orders fetched successfully:', response);
                const res = response as { orders?: any[] };

                if (res.orders && res.orders.length > 0) {
                    this.invoices = res.orders.map((order: any) => ({
                        ...order,
                        items: order.items?.map((item: any) => ({
                            ...item,
                            name: item.sku,
                            price: item.sale_price,
                            quantity: item.quantity
                        })) || []
                    }));

                    // Set first order's items as default selection if available
                    this.ordersData = this.invoices[0];
                    this.cartItems = this.ordersData.items || [];
                } else {
                    console.log('No orders found in response');
                    this.invoices = [];
                    this.cartItems = [];
                }
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;

                // Fallback to mock data for development
                this.createMockData();
            }
        });
    }

    // This method creates mock data when API fails
    // We use a loop to generate multiple sample orders to simulate real data
    // Each order needs unique details like ID, customer, date etc.
    // prepareInvoices(count: number = 20) {
    //     this.invoices = [];
    //     for (let i = 0; i < count; i++) {
    //         const items = this.items[],
    //         const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
    //         this.invoices.push({
    //             order_id: order_id,
    //             customer_name: customer_name,
    //             created_at: new Date(Date.now()).toISOString(),
    //             status: '',
    //             total_amount: total,
    //             items: items
    //         });
    //     }
    //     this.cartItems = this.invoices[0].items;
    // }
    // addDefaultItemsToCart(){
    //     const allProducts = this.data.allProducts;
    //     const items: any[] = [];
    //     const itemsToAdd = Math.floor(Math.random() * 10) + 1;
    //     const availableProducts = [...allProducts];
    //     for (let i = 0; i < itemsToAdd; i++) {
    //         const randomIndex = Math.floor(Math.random() * availableProducts.length);
    //         const randomProduct = availableProducts[randomIndex];
    //         const quantity = Math.floor(Math.random() * 5) + 1;
    //         const checkExist = items.find(item => item.id === randomProduct.id);
    //         if(!checkExist){
    //             items.push({...randomProduct, quantity: quantity});
    //         }
    //         availableProducts.splice(randomIndex, 1);
    //         if (availableProducts.length === 0) {
    //             break;
    //         }
    //     }
    //     return items;
    // }
    onSelect(event: any){
        console.log('Order selected:', event);
        // Handle both real API data and mock data structure
        this.ordersData = event?.event?.data || {};
        this.cartItems = this.ordersData?.items || [];
        console.log('Selected order data:', this.ordersData);
        console.log('Cart items:', this.cartItems);
    }

    createMockData() {
        console.log('Creating mock data for testing...');
        this.invoices = [
            {
                order_id: 'ORD-001',
                customer_name: 'John Doe',
                created_at: '2024-01-15T10:30:00Z',
                status: 'completed',
                total_amount: 150.00,
                items: [
                    {
                        sku: 'PROD-001',
                        name: 'PROD-001',
                        quantity: 2,
                        sale_price: 50.00,
                        price: 50.00
                    },
                    {
                        sku: 'PROD-002',
                        name: 'PROD-002',
                        quantity: 1,
                        sale_price: 50.00,
                        price: 50.00
                    }
                ]
            },
            {
                order_id: 'ORD-002',
                customer_name: 'Jane Smith',
                created_at: '2024-01-16T14:20:00Z',
                status: 'pending',
                total_amount: 75.00,
                items: [
                    {
                        sku: 'PROD-003',
                        name: 'PROD-003',
                        quantity: 1,
                        sale_price: 75.00,
                        price: 75.00
                    }
                ]
            }
        ];

        // Set first order as default selection
        if (this.invoices.length > 0) {
            this.ordersData = this.invoices[0];
            this.cartItems = this.ordersData.items || [];
        }
    }
}