import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any = {};

    constructor(
        private data: DataService,
        private commonService: CommonService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.loadOrders();
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        // Set auth token for API call (same as in checkout)
        localStorage.setItem('auth_token', 'test-token');

        this.commonService.get('get_all_orders').subscribe({
            next: (response) => {
                console.log('Orders fetched successfully:', response);
                const res = response as { orders?: any[] };
                    this.invoices = res.orders?.
                    map((order: any) => ({
                        ...order,
                        items: order.items.map((item: any) => ({
                            ...item,
                            name: item.sku,
                            price: item.sale_price,
                            quantity: item.quantity
                        }))
                    })) || [];

                // Set first order's items as default selection if available
                if (this.invoices.length > 0) {
                    this.ordersData = this.invoices[0];
                    this.cartItems = this.ordersData.items || [];
                }
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;

                // Fallback to mock data for development
                
            }
        });
    }

    // This method creates mock data when API fails
    // We use a loop to generate multiple sample orders to simulate real data
    // Each order needs unique details like ID, customer, date etc.
    // prepareInvoices(count: number = 20) {
    //     this.invoices = [];
    //     for (let i = 0; i < count; i++) {
    //         const items = this.items[],
    //         const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
    //         this.invoices.push({
    //             order_id: order_id,
    //             customer_name: customer_name,
    //             created_at: new Date(Date.now()).toISOString(),
    //             status: '',
    //             total_amount: total,
    //             items: items
    //         });
    //     }
    //     this.cartItems = this.invoices[0].items;
    // }
    // addDefaultItemsToCart(){
    //     const allProducts = this.data.allProducts;
    //     const items: any[] = [];
    //     const itemsToAdd = Math.floor(Math.random() * 10) + 1;
    //     const availableProducts = [...allProducts];
    //     for (let i = 0; i < itemsToAdd; i++) {
    //         const randomIndex = Math.floor(Math.random() * availableProducts.length);
    //         const randomProduct = availableProducts[randomIndex];
    //         const quantity = Math.floor(Math.random() * 5) + 1;
    //         const checkExist = items.find(item => item.id === randomProduct.id);
    //         if(!checkExist){
    //             items.push({...randomProduct, quantity: quantity});
    //         }
    //         availableProducts.splice(randomIndex, 1);
    //         if (availableProducts.length === 0) {
    //             break;
    //         }
    //     }
    //     return items;
    // }
    onSelect(event: any){
        // Handle both real API data and mock data structure
        this.ordersData = event?.event?.data || {};
        this.cartItems = this.ordersData?.items || [];
    }
}